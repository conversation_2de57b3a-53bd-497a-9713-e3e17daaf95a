const admin = require("firebase-admin");

// Initialize Firebase Admin SDK
// Ganti dengan path ke service account key Anda
const serviceAccount = require("./simdoc-db-seeder/credentials.json");

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: `https://${serviceAccount.project_id}-default-rtdb.firebaseio.com`,
});

const auth = admin.auth();
const db = admin.firestore();

async function createTestUser() {
  try {
    console.log("Creating test user...");

    // Create user in Firebase Auth
    const userRecord = await auth.createUser({
      email: "<EMAIL>",
      password: "password123",
      displayName: "Test Admin",
      emailVerified: true,
    });

    console.log("User created in Firebase Auth:", userRecord.uid);

    // Create user document in Firestore
    const userData = {
      id: userRecord.uid,
      fullName: "Test Admin",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      createdBy: "system",
      createdAt: admin.firestore.Timestamp.now(),
      lastLogin: null,
      permissions: {
        canCreateUser: true,
        canEditUser: true,
        canDeleteUser: true,
        canViewUser: true,
        canCreateCategory: true,
        canEditCategory: true,
        canDeleteCategory: true,
        canViewCategory: true,
        canUploadDocument: true,
        canEditDocument: true,
        canDeleteDocument: true,
        canViewDocument: true,
        canViewActivity: true,
        canManageSystem: true,
      },
    };

    await db.collection("users").doc(userRecord.uid).set(userData);
    console.log("User document created in Firestore");

    // Create regular user
    const userRecord2 = await auth.createUser({
      email: "<EMAIL>",
      password: "password123",
      displayName: "Test User",
      emailVerified: true,
    });

    console.log("Regular user created in Firebase Auth:", userRecord2.uid);

    const userData2 = {
      id: userRecord2.uid,
      fullName: "Test User",
      email: "<EMAIL>",
      role: "user",
      status: "active",
      createdBy: userRecord.uid,
      createdAt: admin.firestore.Timestamp.now(),
      lastLogin: null,
      permissions: {
        canCreateUser: false,
        canEditUser: false,
        canDeleteUser: false,
        canViewUser: false,
        canCreateCategory: false,
        canEditCategory: false,
        canDeleteCategory: false,
        canViewCategory: true,
        canUploadDocument: true,
        canEditDocument: false,
        canDeleteDocument: false,
        canViewDocument: true,
        canViewActivity: false,
        canManageSystem: false,
      },
    };

    await db.collection("users").doc(userRecord2.uid).set(userData2);
    console.log("Regular user document created in Firestore");

    console.log("\n=== Test Users Created Successfully ===");
    console.log("Admin User:");
    console.log("  Email: <EMAIL>");
    console.log("  Password: password123");
    console.log("  Role: admin");
    console.log("\nRegular User:");
    console.log("  Email: <EMAIL>");
    console.log("  Password: password123");
    console.log("  Role: user");
    console.log("\nYou can now login with these credentials.");
  } catch (error) {
    console.error("Error creating test user:", error);

    if (error.code === "auth/email-already-exists") {
      console.log(
        "\nUser already exists. Trying to update Firestore document..."
      );

      try {
        // Get existing user
        const existingUser = await auth.getUserByEmail("<EMAIL>");

        // Update Firestore document
        const userData = {
          id: existingUser.uid,
          fullName: "Test Admin",
          email: "<EMAIL>",
          role: "admin",
          status: "active",
          createdBy: "system",
          createdAt: admin.firestore.Timestamp.now(),
          lastLogin: null,
          permissions: {
            canCreateUser: true,
            canEditUser: true,
            canDeleteUser: true,
            canViewUser: true,
            canCreateCategory: true,
            canEditCategory: true,
            canDeleteCategory: true,
            canViewCategory: true,
            canUploadDocument: true,
            canEditDocument: true,
            canDeleteDocument: true,
            canViewDocument: true,
            canViewActivity: true,
            canManageSystem: true,
          },
        };

        await db
          .collection("users")
          .doc(existingUser.uid)
          .set(userData, { merge: true });
        console.log("Firestore document updated for existing user");

        console.log("\n=== Login Credentials ===");
        console.log("Email: <EMAIL>");
        console.log("Password: password123");
      } catch (updateError) {
        console.error("Error updating existing user:", updateError);
      }
    }
  }
}

// Run the function
createTestUser()
  .then(() => {
    console.log("\nScript completed.");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Script failed:", error);
    process.exit(1);
  });
